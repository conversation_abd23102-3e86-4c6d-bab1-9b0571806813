#!/usr/bin/env python3
"""
Database Health Monitoring Script

This script monitors database health and prevents corruption by:
1. Regular integrity checks
2. Automatic WAL checkpoint management
3. Connection monitoring
4. Automatic backup creation
5. Performance monitoring
"""

import sqlite3
import os
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_health.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseHealthMonitor:
    """Monitor and maintain database health."""
    
    def __init__(self, db_path: str = "./erdb_main.db"):
        self.db_path = db_path
        self.backup_dir = "./backups"
        self.health_log = []
        self.running = False
        self.monitor_thread = None
        
        # Ensure backup directory exists
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def check_integrity(self) -> bool:
        """Check database integrity."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('PRAGMA integrity_check;')
            result = cursor.fetchall()
            
            conn.close()
            
            if result and result[0][0] == 'ok':
                logger.debug("Database integrity check: OK")
                return True
            else:
                logger.error("Database integrity check FAILED:")
                for row in result:
                    logger.error(f"  {row[0]}")
                return False
                
        except Exception as e:
            logger.error(f"Error checking database integrity: {str(e)}")
            return False
    
    def checkpoint_wal(self) -> bool:
        """Checkpoint WAL file to main database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get WAL file size before checkpoint
            cursor.execute('PRAGMA wal_checkpoint(TRUNCATE);')
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                busy, log_pages, checkpointed = result
                if busy == 0:
                    logger.debug(f"WAL checkpoint successful: {checkpointed} pages checkpointed")
                    return True
                else:
                    logger.warning(f"WAL checkpoint partially successful: {checkpointed}/{log_pages} pages")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checkpointing WAL: {str(e)}")
            return False
    
    def get_database_stats(self) -> Dict:
        """Get database statistics."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            stats = {}
            
            # Database size
            stats['db_size'] = os.path.getsize(self.db_path)
            
            # WAL file size
            wal_path = self.db_path + '-wal'
            stats['wal_size'] = os.path.getsize(wal_path) if os.path.exists(wal_path) else 0
            
            # Page count
            cursor.execute('PRAGMA page_count;')
            stats['page_count'] = cursor.fetchone()[0]
            
            # Page size
            cursor.execute('PRAGMA page_size;')
            stats['page_size'] = cursor.fetchone()[0]
            
            # Free pages
            cursor.execute('PRAGMA freelist_count;')
            stats['free_pages'] = cursor.fetchone()[0]
            
            # Journal mode
            cursor.execute('PRAGMA journal_mode;')
            stats['journal_mode'] = cursor.fetchone()[0]
            
            # Table count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table';")
            stats['table_count'] = cursor.fetchone()[0]
            
            conn.close()
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {str(e)}")
            return {}
    
    def create_backup(self) -> Optional[str]:
        """Create a backup of the database."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"health_backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # Use SQLite backup API for consistent backup
            source = sqlite3.connect(self.db_path)
            backup = sqlite3.connect(backup_path)
            
            source.backup(backup)
            
            source.close()
            backup.close()
            
            logger.info(f"Database backup created: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Error creating backup: {str(e)}")
            return None
    
    def cleanup_old_backups(self, retention_days: int = 7):
        """Remove backups older than retention period."""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('health_backup_') and filename.endswith('.db'):
                    file_path = os.path.join(self.backup_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        logger.info(f"Removed old backup: {filename}")
                        
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {str(e)}")
    
    def health_check(self) -> Dict:
        """Perform comprehensive health check."""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'integrity_ok': False,
            'wal_checkpoint_ok': False,
            'stats': {},
            'issues': []
        }
        
        # Check integrity
        health_status['integrity_ok'] = self.check_integrity()
        if not health_status['integrity_ok']:
            health_status['issues'].append('Database integrity check failed')
        
        # Checkpoint WAL
        health_status['wal_checkpoint_ok'] = self.checkpoint_wal()
        if not health_status['wal_checkpoint_ok']:
            health_status['issues'].append('WAL checkpoint failed')
        
        # Get stats
        health_status['stats'] = self.get_database_stats()
        
        # Check for large WAL file
        if health_status['stats'].get('wal_size', 0) > 50 * 1024 * 1024:  # 50MB
            health_status['issues'].append('WAL file is very large')
        
        # Check for excessive free pages
        free_pages = health_status['stats'].get('free_pages', 0)
        total_pages = health_status['stats'].get('page_count', 1)
        if free_pages > total_pages * 0.3:  # More than 30% free pages
            health_status['issues'].append('Excessive free pages - consider VACUUM')
        
        # Log health status
        if health_status['issues']:
            logger.warning(f"Database health issues detected: {health_status['issues']}")
        else:
            logger.info("Database health check: All OK")
        
        # Store in health log
        self.health_log.append(health_status)
        
        # Keep only last 100 entries
        if len(self.health_log) > 100:
            self.health_log = self.health_log[-100:]
        
        return health_status
    
    def start_monitoring(self, check_interval: int = 300):  # 5 minutes
        """Start continuous health monitoring."""
        self.running = True
        
        def monitor_loop():
            while self.running:
                try:
                    self.health_check()
                    time.sleep(check_interval)
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {str(e)}")
                    time.sleep(60)  # Wait 1 minute before retrying
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info(f"Database health monitoring started (interval: {check_interval}s)")
    
    def stop_monitoring(self):
        """Stop health monitoring."""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        logger.info("Database health monitoring stopped")
    
    def get_health_report(self) -> Dict:
        """Get comprehensive health report."""
        if not self.health_log:
            return {"error": "No health data available"}
        
        latest = self.health_log[-1]
        
        # Calculate trends
        recent_checks = self.health_log[-10:] if len(self.health_log) >= 10 else self.health_log
        integrity_success_rate = sum(1 for check in recent_checks if check['integrity_ok']) / len(recent_checks)
        
        return {
            'latest_check': latest,
            'integrity_success_rate': integrity_success_rate,
            'total_checks': len(self.health_log),
            'recent_issues': [check['issues'] for check in recent_checks if check['issues']]
        }

def setup_scheduled_maintenance():
    """Setup scheduled maintenance tasks."""
    monitor = DatabaseHealthMonitor()

    # Note: For production use, consider using cron jobs or system scheduler
    # instead of the schedule library for better reliability

    logger.info("Manual maintenance tasks available - use cron for scheduling")

    return monitor

if __name__ == "__main__":
    # Start monitoring
    monitor = DatabaseHealthMonitor()
    
    # Perform initial health check
    print("Performing initial database health check...")
    health_status = monitor.health_check()
    
    print(f"\nHealth Status:")
    print(f"  Integrity: {'✓' if health_status['integrity_ok'] else '✗'}")
    print(f"  WAL Checkpoint: {'✓' if health_status['wal_checkpoint_ok'] else '✗'}")
    print(f"  Issues: {len(health_status['issues'])}")
    
    if health_status['issues']:
        print("  Issues found:")
        for issue in health_status['issues']:
            print(f"    - {issue}")
    
    stats = health_status['stats']
    if stats:
        print(f"\nDatabase Statistics:")
        print(f"  Size: {stats.get('db_size', 0):,} bytes")
        print(f"  WAL Size: {stats.get('wal_size', 0):,} bytes")
        print(f"  Pages: {stats.get('page_count', 0):,}")
        print(f"  Free Pages: {stats.get('free_pages', 0):,}")
        print(f"  Tables: {stats.get('table_count', 0)}")
    
    # Create backup
    print("\nCreating backup...")
    backup_path = monitor.create_backup()
    if backup_path:
        print(f"✓ Backup created: {backup_path}")
    else:
        print("✗ Backup creation failed")
