# Database Configuration for ERDB System
# This file contains database connection settings

[main_database]
# Unified database for all relational data
database_path = "./erdb_main.db"
max_connections = 10
timeout = 30
check_same_thread = false

[vector_databases]
# Separate Chroma databases for vector embeddings
chroma_base_path = "./data/unified_chroma"
categories = ["CANOPY", "MANUAL", "RISE"]

[sqlite_optimizations]
journal_mode = WAL
synchronous = NORMAL
cache_size = 10000
temp_store = MEMORY
mmap_size = 268435456
foreign_keys = ON
busy_timeout = 30000

[backup_settings]
backup_directory = "./backups"
retention_days = 30
auto_backup = true
