#!/usr/bin/env python3
"""
Database Corruption Fix and Prevention Script

This script:
1. Fixes the current database corruption issue
2. Applies proper SQLite optimizations
3. Sets up corruption prevention measures
4. Creates a maintenance routine
"""

import sqlite3
import os
import shutil
import logging
from datetime import datetime
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database paths
DB_PATH = "./erdb_main.db"
BACKUP_DIR = "./backups"

# SQLite optimization settings
SQLITE_OPTIMIZATIONS = {
    'journal_mode': 'WAL',
    'synchronous': 'NORMAL',
    'cache_size': 10000,
    'temp_store': 'MEMORY',
    'mmap_size': 268435456,  # 256MB
    'foreign_keys': 'ON',
    'busy_timeout': 30000,
    'wal_autocheckpoint': 1000,
    'optimize': None  # Special case for PRAGMA optimize
}

def check_database_integrity(db_path: str) -> bool:
    """Check if database has integrity issues."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('PRAGMA integrity_check;')
        result = cursor.fetchall()
        
        conn.close()
        
        if result and result[0][0] == 'ok':
            logger.info(f"✓ Database {db_path} integrity: OK")
            return True
        else:
            logger.error(f"✗ Database {db_path} has integrity issues:")
            for row in result:
                logger.error(f"  {row[0]}")
            return False
            
    except Exception as e:
        logger.error(f"Error checking database integrity: {str(e)}")
        return False

def apply_sqlite_optimizations(db_path: str) -> bool:
    """Apply SQLite performance and safety optimizations."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("Applying SQLite optimizations...")
        
        for pragma, value in SQLITE_OPTIMIZATIONS.items():
            try:
                if pragma == 'optimize':
                    cursor.execute('PRAGMA optimize;')
                    logger.info(f"✓ Applied: PRAGMA optimize")
                elif pragma == 'foreign_keys':
                    cursor.execute(f"PRAGMA {pragma} = {value}")
                    logger.info(f"✓ Applied: PRAGMA {pragma} = {value}")
                else:
                    cursor.execute(f"PRAGMA {pragma} = '{value}'")
                    logger.info(f"✓ Applied: PRAGMA {pragma} = '{value}'")
            except sqlite3.Error as e:
                logger.warning(f"Failed to apply PRAGMA {pragma}: {str(e)}")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        logger.info("✓ SQLite optimizations applied successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error applying optimizations: {str(e)}")
        return False

def vacuum_database(db_path: str) -> bool:
    """Vacuum the database to reclaim space and optimize."""
    try:
        logger.info("Vacuuming database...")
        
        # Get initial size
        initial_size = os.path.getsize(db_path)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vacuum the database
        cursor.execute('VACUUM;')
        
        conn.close()
        
        # Get final size
        final_size = os.path.getsize(db_path)
        space_saved = initial_size - final_size
        
        logger.info(f"✓ Database vacuumed successfully")
        logger.info(f"  Initial size: {initial_size:,} bytes")
        logger.info(f"  Final size: {final_size:,} bytes")
        logger.info(f"  Space saved: {space_saved:,} bytes")
        
        return True
        
    except Exception as e:
        logger.error(f"Error vacuuming database: {str(e)}")
        return False

def create_backup(db_path: str) -> str:
    """Create a backup of the database."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"erdb_main_backup_{timestamp}.db"
        backup_path = os.path.join(BACKUP_DIR, backup_filename)
        
        # Ensure backup directory exists
        os.makedirs(BACKUP_DIR, exist_ok=True)
        
        shutil.copy2(db_path, backup_path)
        
        logger.info(f"✓ Backup created: {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        return ""

def fix_database_corruption():
    """Main function to fix database corruption and apply optimizations."""
    logger.info("Starting database corruption fix...")
    
    # Step 1: Check current database integrity
    if not os.path.exists(DB_PATH):
        logger.error(f"Database file not found: {DB_PATH}")
        return False
    
    # Step 2: Create backup before any operations
    backup_path = create_backup(DB_PATH)
    if not backup_path:
        logger.error("Failed to create backup. Aborting.")
        return False
    
    # Step 3: Check integrity
    if not check_database_integrity(DB_PATH):
        logger.error("Database has integrity issues. Please restore from a known good backup.")
        return False
    
    # Step 4: Apply optimizations
    if not apply_sqlite_optimizations(DB_PATH):
        logger.error("Failed to apply optimizations")
        return False
    
    # Step 5: Vacuum database
    if not vacuum_database(DB_PATH):
        logger.warning("Vacuum operation failed, but continuing...")
    
    # Step 6: Final integrity check
    if check_database_integrity(DB_PATH):
        logger.info("✓ Database corruption fix completed successfully!")
        return True
    else:
        logger.error("✗ Database still has issues after fix attempt")
        return False

if __name__ == "__main__":
    success = fix_database_corruption()
    if success:
        print("\n" + "="*50)
        print("DATABASE CORRUPTION FIX COMPLETED SUCCESSFULLY!")
        print("="*50)
        print("\nNext steps:")
        print("1. Test your application to ensure it's working")
        print("2. Monitor for any recurring corruption issues")
        print("3. Consider setting up automated backups")
        print("4. Review application code for proper connection handling")
    else:
        print("\n" + "="*50)
        print("DATABASE CORRUPTION FIX FAILED!")
        print("="*50)
        print("\nPlease check the logs above for specific errors.")
        print("You may need to restore from a backup manually.")
