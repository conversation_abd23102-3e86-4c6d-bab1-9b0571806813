#!/usr/bin/env python3
"""
Test script to verify citation behavior consistency across all anti-hallucination modes.
This script tests the fixes implemented for citation inconsistencies.
"""

import os
import sys
import json
import logging
from typing import Dict, List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_templates():
    """Test that config templates have consistent citation instructions."""
    print("=== Testing Config Templates ===")
    
    config_file = 'config/default_models.json'
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Check system prompts
    model_params = config.get('model_parameters', {})
    modes = ['strict', 'balanced', 'off']
    
    print("Checking system prompts...")
    for mode in modes:
        if mode in model_params:
            system_prompt = model_params[mode].get('system_prompt', '')
            if 'MUST ALWAYS cite' in system_prompt or 'bracket notation' in system_prompt:
                print(f"✅ {mode} mode system prompt includes citation requirements")
            else:
                print(f"❌ {mode} mode system prompt missing citation requirements")
                return False
        else:
            print(f"❌ {mode} mode not found in model_parameters")
            return False
    
    # Check prompt templates
    prompt_templates = config.get('query_parameters', {}).get('prompt_templates', {})
    
    print("Checking prompt templates...")
    for mode in modes:
        if mode in prompt_templates:
            template = prompt_templates[mode]
            if 'NEVER generate HTML links' in template and 'square brackets' in template:
                print(f"✅ {mode} template uses consistent bracket notation")
            else:
                print(f"❌ {mode} template has inconsistent citation instructions")
                print(f"   Template preview: {template[:200]}...")
                return False
        else:
            print(f"❌ {mode} template not found")
            return False
    
    # Check general and document_specific templates
    general_template = prompt_templates.get('general', '')
    doc_template = prompt_templates.get('document_specific', '')
    
    if 'MUST ALWAYS cite' in general_template:
        print("✅ General template includes citation requirements")
    else:
        print("❌ General template missing citation requirements")
        return False
    
    if 'MUST ALWAYS cite' in doc_template:
        print("✅ Document-specific template includes citation requirements")
    else:
        print("❌ Document-specific template missing citation requirements")
        return False
    
    return True

def test_query_service_consistency():
    """Test that query service has consistent citation instructions."""
    print("\n=== Testing Query Service Consistency ===")
    
    query_service_file = 'app/services/query_service.py'
    if not os.path.exists(query_service_file):
        print(f"❌ Query service file not found: {query_service_file}")
        return False
    
    with open(query_service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for conflicting HTML generation instructions in prompt templates
    # (Exclude the citation conversion function which legitimately generates HTML)
    html_conflicts = [
        'According to <a href=',
        'The format MUST be: \'According to <a href'
    ]

    conflicts_found = []
    for conflict in html_conflicts:
        if conflict in content:
            conflicts_found.append(conflict)

    # Check for HTML instructions in prompt templates specifically (not in conversion functions)
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if 'target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline"' in line:
            # Check if this is in a prompt template (not in the conversion function)
            context_lines = lines[max(0, i-10):i+10]
            context_text = '\n'.join(context_lines)
            if 'prompt_template' in context_text and 'convert_citation_placeholders' not in context_text:
                conflicts_found.append('HTML generation in prompt template')
    
    if conflicts_found:
        print(f"❌ Found conflicting HTML generation instructions:")
        for conflict in conflicts_found:
            print(f"   - {conflict}")
        return False
    else:
        print("✅ No conflicting HTML generation instructions found")
    
    # Check for consistent bracket notation instructions
    bracket_instructions = [
        'NEVER generate HTML links or <a> tags',
        'use ONLY the Citation Filename in square brackets',
        'bracket notation'
    ]
    
    bracket_found = []
    for instruction in bracket_instructions:
        if instruction in content:
            bracket_found.append(instruction)
    
    if len(bracket_found) >= 2:
        print("✅ Consistent bracket notation instructions found")
    else:
        print(f"❌ Missing bracket notation instructions. Found: {bracket_found}")
        return False
    
    # Check for citation validation function
    if 'validate_citations_present' in content:
        print("✅ Citation validation function found")
    else:
        print("❌ Citation validation function missing")
        return False
    
    return True

def test_citation_conversion_system():
    """Test that citation conversion system is properly integrated."""
    print("\n=== Testing Citation Conversion System ===")
    
    query_service_file = 'app/services/query_service.py'
    with open(query_service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for citation conversion function
    if 'convert_citation_placeholders' in content:
        print("✅ Citation conversion function found")
    else:
        print("❌ Citation conversion function missing")
        return False
    
    # Check for citation conversion call in processing pipeline
    if 'answer = convert_citation_placeholders(answer, docs)' in content:
        print("✅ Citation conversion integrated in processing pipeline")
    else:
        print("❌ Citation conversion not integrated in processing pipeline")
        return False
    
    # Check for citation validation call
    if 'validate_citations_present(answer, docs' in content:
        print("✅ Citation validation integrated in processing pipeline")
    else:
        print("❌ Citation validation not integrated in processing pipeline")
        return False
    
    return True

def main():
    """Main test function."""
    print("Citation Consistency Test")
    print("=" * 50)
    
    success = True
    
    # Test config templates
    if not test_config_templates():
        success = False
    
    # Test query service consistency
    if not test_query_service_consistency():
        success = False
    
    # Test citation conversion system
    if not test_citation_conversion_system():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL TESTS PASSED - Citation behavior should now be consistent!")
        print("\nKey improvements implemented:")
        print("1. ✅ System prompts now include citation requirements for all modes")
        print("2. ✅ All prompt templates use consistent bracket notation")
        print("3. ✅ Removed conflicting HTML generation instructions")
        print("4. ✅ Added citation validation to ensure citations are always provided")
        print("5. ✅ Citation conversion system properly integrated")
    else:
        print("❌ SOME TESTS FAILED - Please review the issues above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
